* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

/* خلفية متحركة */
.background-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../img/WallpaperDog-20526055.jpg') center/cover;
    opacity: 0.1;
    z-index: -1;
    animation: backgroundMove 20s ease-in-out infinite;
}

@keyframes backgroundMove {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(1deg); }
}

.container {
    max-width: 500px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.step {
    display: none;
    width: 100%;
    animation: fadeInUp 0.6s ease-out;
}

.step.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* الخطوة 1: شاشة التحميل */
.loading-screen {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.game-logo {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.loading-screen h1 {
    color: #2c3e50;
    font-size: 2.5em;
    margin-bottom: 10px;
    font-weight: 700;
}

.loading-screen h2 {
    color: #7f8c8d;
    font-size: 1.2em;
    margin-bottom: 30px;
    font-weight: 400;
}

.loading-spinner {
    margin: 30px 0;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #ecf0f1;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #7f8c8d;
    font-size: 1.1em;
    margin-bottom: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #ecf0f1;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    width: 0%;
    animation: progressFill 3s ease-in-out forwards;
}

@keyframes progressFill {
    0% { width: 0%; }
    100% { width: 100%; }
}

/* الخطوة 2: النموذج */
.form-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    text-align: center;
}

.form-logo {
    width: 60px;
    height: 60px;
    margin-bottom: 20px;
}

.form-container h2 {
    color: #2c3e50;
    font-size: 2em;
    margin-bottom: 30px;
    font-weight: 600;
}

.input-group {
    margin-bottom: 25px;
    text-align: right;
}

.input-group label {
    display: block;
    color: #34495e;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 1.1em;
}

.input-group input,
.input-group select {
    width: 100%;
    padding: 15px;
    border: 2px solid #ecf0f1;
    border-radius: 10px;
    font-size: 1.1em;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    transform: translateY(-2px);
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2ecc71);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 25px;
    font-size: 1.2em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 20px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(52, 152, 219, 0.4);
}

/* الخطوة 3: النجاح */
.success-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    text-align: center;
}

.success-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2.5em;
    color: white;
    animation: successPulse 1.5s ease-in-out;
}

@keyframes successPulse {
    0% { transform: scale(0); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.success-container h2 {
    color: #2c3e50;
    font-size: 2em;
    margin-bottom: 15px;
    font-weight: 600;
}

.success-container p {
    color: #7f8c8d;
    font-size: 1.1em;
    margin-bottom: 25px;
}

.user-info {
    background: rgba(46, 204, 113, 0.1);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 25px;
    text-align: right;
}

.user-info p {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.1em;
}

.user-info strong {
    font-weight: 600;
}

/* الخطوة 4: اختيار العملات */
.coins-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    text-align: center;
}

.coins-container h2 {
    color: #2c3e50;
    font-size: 2em;
    margin-bottom: 30px;
    font-weight: 600;
}

.coins-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 30px;
}

.coin-option {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    padding: 25px 15px;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 20px rgba(243, 156, 18, 0.3);
}

.coin-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(243, 156, 18, 0.4);
}

.coin-option.selected {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    box-shadow: 0 15px 30px rgba(46, 204, 113, 0.4);
}

.coin-icon {
    font-size: 2em;
    margin-bottom: 10px;
}

.coin-option span {
    font-size: 1.1em;
    font-weight: 600;
}

/* الخطوة 5: التوليد */
.generating-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    text-align: center;
}

.generating-container h2 {
    color: #2c3e50;
    font-size: 2em;
    margin-bottom: 30px;
    font-weight: 600;
}

.generation-progress {
    margin-bottom: 40px;
}

.progress-circle {
    position: relative;
    display: inline-block;
}

.progress-ring {
    transform: rotate(-90deg);
}

.progress-ring-circle {
    transition: stroke-dasharray 0.35s;
    stroke-dasharray: 0 326.56;
    stroke-dashoffset: 0;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.5em;
    font-weight: 600;
    color: #2c3e50;
}

.generation-steps {
    text-align: right;
}

.gen-step {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    margin-bottom: 10px;
    background: rgba(52, 152, 219, 0.1);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.gen-step.completed {
    background: rgba(46, 204, 113, 0.1);
}

.gen-step.active {
    background: rgba(243, 156, 18, 0.1);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.step-icon {
    font-size: 1.5em;
    margin-left: 15px;
}

.step-text {
    flex: 1;
    font-size: 1.1em;
    color: #2c3e50;
    font-weight: 500;
}

.step-status {
    font-size: 1.2em;
}

/* الخطوة 6: النهاية */
.final-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    text-align: center;
}

.final-icon {
    font-size: 4em;
    margin-bottom: 20px;
    animation: celebration 2s ease-in-out infinite;
}

@keyframes celebration {
    0%, 100% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.1) rotate(-5deg); }
    75% { transform: scale(1.1) rotate(5deg); }
}

.final-container h2 {
    color: #2c3e50;
    font-size: 2.5em;
    margin-bottom: 15px;
    font-weight: 700;
}

.final-container p {
    color: #7f8c8d;
    font-size: 1.2em;
    margin-bottom: 25px;
}

.final-info {
    background: rgba(46, 204, 113, 0.1);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 25px;
    text-align: right;
}

.final-info p {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.2em;
}

.final-note {
    background: rgba(243, 156, 18, 0.1);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 25px;
    border-right: 4px solid #f39c12;
}

.final-note p {
    color: #e67e22;
    font-size: 1em;
    margin: 0;
}

.btn-large {
    padding: 20px 50px;
    font-size: 1.3em;
}

/* تأثيرات متجاوبة */
@media (max-width: 600px) {
    .container {
        padding: 10px;
    }

    .coins-options {
        grid-template-columns: 1fr;
    }

    .loading-screen,
    .form-container,
    .success-container,
    .coins-container,
    .generating-container,
    .final-container {
        padding: 25px;
    }
}
