* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON>o', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

/* خلفية متحركة */
.background-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../img/WallpaperDog-20526055.jpg') center/cover;
    opacity: 0.1;
    z-index: -1;
    animation: backgroundMove 20s ease-in-out infinite;
}

@keyframes backgroundMove {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(1deg); }
}

.container {
    max-width: 500px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.step {
    display: none;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    backface-visibility: hidden;
    transform-style: preserve-3d;
    transition: transform 0.8s ease-in-out;
}

.step.active {
    display: block;
    transform: rotateY(0deg);
}

.step.flip-out {
    transform: rotateY(-180deg);
}

.step.flip-in {
    transform: rotateY(180deg);
}

.step.flip-in.active {
    transform: rotateY(0deg);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* الخطوة 1: شاشة التحميل */
.loading-screen {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.game-logo {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.loading-screen h1 {
    color: #2c3e50;
    font-size: 2.5em;
    margin-bottom: 10px;
    font-weight: 700;
}

.loading-screen h2 {
    color: #7f8c8d;
    font-size: 1.2em;
    margin-bottom: 30px;
    font-weight: 400;
}

.loading-spinner {
    margin: 30px 0;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #ecf0f1;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #7f8c8d;
    font-size: 1.1em;
    margin-bottom: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #ecf0f1;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    width: 0%;
    animation: progressFill 3s ease-in-out forwards;
}

@keyframes progressFill {
    0% { width: 0%; }
    100% { width: 100%; }
}

/* الخطوة 2: النموذج */
.form-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    text-align: center;
}

.form-logo {
    width: 60px;
    height: 60px;
    margin-bottom: 20px;
}

.form-container h2 {
    color: #2c3e50;
    font-size: 2em;
    margin-bottom: 30px;
    font-weight: 600;
}

.input-group {
    margin-bottom: 25px;
    text-align: right;
}

.input-group label {
    display: block;
    color: #34495e;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 1.1em;
}

.input-group input,
.input-group select {
    width: 100%;
    padding: 15px;
    border: 2px solid #ecf0f1;
    border-radius: 10px;
    font-size: 1.1em;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    transform: translateY(-2px);
}

.input-error {
    color: #e74c3c;
    font-size: 0.9em;
    margin-top: 5px;
    display: none;
}

.input-group.error .input-error {
    display: block;
}

.input-group.error input {
    border-color: #e74c3c;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2ecc71);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 25px;
    font-size: 1.2em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 20px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(52, 152, 219, 0.4);
}

/* الخطوة 3: النجاح */
.success-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    text-align: center;
}

.success-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2.5em;
    color: white;
    animation: successPulse 1.5s ease-in-out;
}

@keyframes successPulse {
    0% { transform: scale(0); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.success-container h2 {
    color: #2c3e50;
    font-size: 2em;
    margin-bottom: 15px;
    font-weight: 600;
}

.success-container p {
    color: #7f8c8d;
    font-size: 1.1em;
    margin-bottom: 25px;
}

.user-info {
    background: rgba(46, 204, 113, 0.1);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 25px;
    text-align: right;
}

.user-info p {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.1em;
}

.user-info strong {
    font-weight: 600;
}

/* Step 4: Gems selection */
.gems-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    text-align: center;
    max-width: 400px;
    margin: 0 auto;
}

.gems-header {
    margin-bottom: 30px;
}

.step-number {
    background: #8e44ad;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5em;
    font-weight: bold;
    margin: 0 auto 15px;
}

.gems-header h2 {
    color: #7f8c8d;
    font-size: 1.2em;
    font-weight: 400;
    margin: 0;
}

.selected-amount {
    background: rgba(142, 68, 173, 0.1);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 25px;
}

.selected-label {
    background: #8e44ad;
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.8em;
    font-weight: bold;
    display: inline-block;
    margin-bottom: 15px;
}

.gem-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.gem-icon {
    font-size: 2.5em;
    filter: drop-shadow(0 0 10px rgba(46, 204, 113, 0.5));
}

.gem-count {
    font-size: 3em;
    font-weight: bold;
    color: #2c3e50;
}

.gem-text {
    font-size: 1.2em;
    color: #7f8c8d;
    font-weight: 500;
}

.gems-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 25px;
}

.gem-option {
    background: white;
    border: 2px solid #ecf0f1;
    padding: 15px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
}

.gem-option:hover {
    border-color: #8e44ad;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(142, 68, 173, 0.2);
}

.gem-option.selected {
    border-color: #8e44ad;
    background: rgba(142, 68, 173, 0.1);
}

.gem-icon-small {
    font-size: 1.2em;
    color: #27ae60;
}

.gem-option span {
    font-size: 1em;
    color: #2c3e50;
    font-weight: 500;
}

.btn-proceed {
    background: linear-gradient(135deg, #8e44ad, #9b59b6);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 25px;
    font-size: 1.2em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 20px rgba(142, 68, 173, 0.3);
    width: 100%;
}

.btn-proceed:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(142, 68, 173, 0.4);
}

/* الخطوة 5: التوليد */
.generating-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    text-align: center;
}

.generating-container h2 {
    color: #2c3e50;
    font-size: 2em;
    margin-bottom: 30px;
    font-weight: 600;
}

.generation-progress {
    margin-bottom: 40px;
}

.progress-circle {
    position: relative;
    display: inline-block;
}

.progress-ring {
    transform: rotate(-90deg);
}

.progress-ring-circle {
    transition: stroke-dasharray 0.35s;
    stroke-dasharray: 0 326.56;
    stroke-dashoffset: 0;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.5em;
    font-weight: 600;
    color: #2c3e50;
}

.generation-steps {
    text-align: right;
}

.gen-step {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    margin-bottom: 10px;
    background: rgba(52, 152, 219, 0.1);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.gen-step.completed {
    background: rgba(46, 204, 113, 0.1);
}

.gen-step.active {
    background: rgba(243, 156, 18, 0.1);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.step-icon {
    font-size: 1.5em;
    margin-left: 15px;
}

.step-text {
    flex: 1;
    font-size: 1.1em;
    color: #2c3e50;
    font-weight: 500;
}

.step-status {
    font-size: 1.2em;
}

/* الخطوة 6: النهاية */
.final-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    text-align: center;
}

.final-icon {
    font-size: 4em;
    margin-bottom: 20px;
    animation: celebration 2s ease-in-out infinite;
}

@keyframes celebration {
    0%, 100% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.1) rotate(-5deg); }
    75% { transform: scale(1.1) rotate(5deg); }
}

.final-container h2 {
    color: #2c3e50;
    font-size: 2.5em;
    margin-bottom: 15px;
    font-weight: 700;
}

.final-container p {
    color: #7f8c8d;
    font-size: 1.2em;
    margin-bottom: 25px;
}

.final-info {
    background: rgba(46, 204, 113, 0.1);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 25px;
    text-align: right;
}

.final-info p {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.2em;
}

.final-note {
    background: rgba(243, 156, 18, 0.1);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 25px;
    border-right: 4px solid #f39c12;
}

.final-note p {
    color: #e67e22;
    font-size: 1em;
    margin: 0;
}

.btn-large {
    padding: 20px 50px;
    font-size: 1.3em;
}

/* تأثيرات متجاوبة */
@media (max-width: 600px) {
    .container {
        padding: 10px;
    }

    .coins-options {
        grid-template-columns: 1fr;
    }

    .loading-screen,
    .form-container,
    .success-container,
    .coins-container,
    .generating-container,
    .final-container {
        padding: 25px;
    }
}
