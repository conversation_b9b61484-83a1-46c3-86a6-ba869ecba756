// Global variables
let currentStep = 1;
let selectedAmount = 0;
let username = '';
let platform = '';

// Start application
document.addEventListener('DOMContentLoaded', function() {
    // Start loading screen
    setTimeout(() => {
        nextStep();
    }, 4000);
});

// Transition to next step with flip effect
function nextStep() {
    const currentStepElement = document.getElementById(`step${currentStep}`);

    // Add flip out animation
    currentStepElement.classList.add('flip-out');

    setTimeout(() => {
        currentStepElement.classList.remove('active', 'flip-out');
        currentStep++;

        const nextStepElement = document.getElementById(`step${currentStep}`);
        if (nextStepElement) {
            nextStepElement.classList.add('flip-in');
            nextStepElement.classList.add('active');

            setTimeout(() => {
                nextStepElement.classList.remove('flip-in');
            }, 50);
        }
    }, 400);
}

// Form validation and handling
document.getElementById('userForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const usernameInput = document.getElementById('username');
    const platformSelect = document.getElementById('platform');
    const usernameGroup = usernameInput.parentElement;

    username = usernameInput.value.trim();
    platform = platformSelect.value;

    // Reset error states
    usernameGroup.classList.remove('error');

    // Validate username (minimum 4 characters)
    if (username.length < 4) {
        usernameGroup.classList.add('error');
        usernameInput.focus();
        return;
    }

    if (username && platform) {
        // Display data in success screen
        document.getElementById('displayUsername').textContent = username;
        document.getElementById('displayPlatform').textContent = platform;

        nextStep();
    }
});

// Real-time username validation
document.getElementById('username').addEventListener('input', function() {
    const usernameGroup = this.parentElement;
    if (this.value.trim().length >= 4) {
        usernameGroup.classList.remove('error');
    }
});

// Handle gems selection
document.addEventListener('DOMContentLoaded', function() {
    const gemOptions = document.querySelectorAll('.gem-option');
    const gemCount = document.querySelector('.gem-count');

    // Set default selection
    selectedAmount = 2500;
    if (gemOptions.length > 0) {
        gemOptions[0].classList.add('selected');
    }

    gemOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove selection from all options
            gemOptions.forEach(opt => opt.classList.remove('selected'));

            // Add selection to clicked option
            this.classList.add('selected');

            selectedAmount = this.getAttribute('data-amount');

            // Update display
            gemCount.textContent = selectedAmount;
        });
    });
});

// Proceed to generation
function proceedToGeneration() {
    nextStep();
    startGeneration();
}

// Start fake generation process
function startGeneration() {
    const steps = document.querySelectorAll('.gen-step');
    const progressRing = document.querySelector('.progress-ring-circle');
    const progressText = document.querySelector('.progress-text');

    let progress = 0;
    let stepIndex = 0;

    // Update progress bar
    const updateProgress = () => {
        progress += Math.random() * 15 + 5;
        if (progress > 100) progress = 100;

        const circumference = 2 * Math.PI * 52;
        const strokeDasharray = `${(progress / 100) * circumference} ${circumference}`;
        progressRing.style.strokeDasharray = strokeDasharray;
        progressText.textContent = Math.round(progress) + '%';

        if (progress < 100) {
            setTimeout(updateProgress, Math.random() * 500 + 300);
        } else {
            setTimeout(() => {
                nextStep();
                showFinalResults();
            }, 1000);
        }
    };

    // Update generation steps
    const updateSteps = () => {
        if (stepIndex < steps.length) {
            // Complete previous step
            if (stepIndex > 0) {
                steps[stepIndex - 1].classList.remove('active');
                steps[stepIndex - 1].classList.add('completed');
                steps[stepIndex - 1].querySelector('.step-status').textContent = '✓';
            }

            // Activate current step
            steps[stepIndex].classList.add('active');
            steps[stepIndex].querySelector('.step-status').textContent = '⏳';

            stepIndex++;

            if (stepIndex < steps.length) {
                setTimeout(updateSteps, Math.random() * 2000 + 1500);
            } else {
                // Complete last step
                setTimeout(() => {
                    steps[stepIndex - 1].classList.remove('active');
                    steps[stepIndex - 1].classList.add('completed');
                    steps[stepIndex - 1].querySelector('.step-status').textContent = '✓';
                }, 1000);
            }
        }
    };

    // Start processes
    updateProgress();
    updateSteps();
}

// Show final results
function showFinalResults() {
    document.getElementById('finalAmount').textContent = selectedAmount.toLocaleString();
    document.getElementById('finalUsername').textContent = username;
}

// Complete process
function completeProcess() {
    // Here you can add a link for human verification or any other action
    alert('Thank you! You will be redirected to complete human verification.');

    // You can add an external link here
    // window.open('https://example.com/verification', '_blank');
}

// Additional effects
document.addEventListener('DOMContentLoaded', function() {
    // Add particle effect to background
    createParticles();

    // Add sound effects (optional)
    addSoundEffects();
});

// Create moving particles
function createParticles() {
    const particlesContainer = document.createElement('div');
    particlesContainer.className = 'particles';
    particlesContainer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: -1;
    `;
    
    document.body.appendChild(particlesContainer);
    
    for (let i = 0; i < 50; i++) {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 50%;
            animation: float ${Math.random() * 3 + 2}s ease-in-out infinite;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation-delay: ${Math.random() * 2}s;
        `;
        
        particlesContainer.appendChild(particle);
    }
    
    // Add CSS for animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 0.5; }
        }
    `;
    document.head.appendChild(style);
}

// Add sound effects (optional)
function addSoundEffects() {
    // You can add sound files here
    // const clickSound = new Audio('assets/sounds/click.mp3');
    // const successSound = new Audio('assets/sounds/success.mp3');

    // Add sounds to buttons
    document.querySelectorAll('button, .gem-option').forEach(element => {
        element.addEventListener('click', () => {
            // clickSound.play();
        });
    });
}

// Additional interaction effects
document.addEventListener('DOMContentLoaded', function() {
    // Shake effect for important elements
    const importantElements = document.querySelectorAll('.btn-primary, .gem-option');

    importantElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            this.style.animation = 'shake 0.5s ease-in-out';
        });

        element.addEventListener('animationend', function() {
            this.style.animation = '';
        });
    });

    // Add CSS for shake
    const shakeStyle = document.createElement('style');
    shakeStyle.textContent = `
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    `;
    document.head.appendChild(shakeStyle);
});

// Performance and experience optimization
window.addEventListener('load', function() {
    // Hide default browser loading screen
    document.body.style.visibility = 'visible';

    // Optimize animation for low-end devices
    if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
        document.body.classList.add('low-performance');
    }
});

// Error handling
window.addEventListener('error', function(e) {
    console.log('An error occurred:', e.error);
    // You can add custom error handling here
});
