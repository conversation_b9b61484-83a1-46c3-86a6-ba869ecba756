// متغيرات عامة
let currentStep = 1;
let selectedAmount = 0;
let username = '';
let platform = '';

// بدء التطبيق
document.addEventListener('DOMContentLoaded', function() {
    // بدء شاشة التحميل
    setTimeout(() => {
        nextStep();
    }, 4000);
});

// الانتقال للخطوة التالية
function nextStep() {
    const currentStepElement = document.getElementById(`step${currentStep}`);
    currentStepElement.classList.remove('active');
    
    currentStep++;
    
    const nextStepElement = document.getElementById(`step${currentStep}`);
    if (nextStepElement) {
        nextStepElement.classList.add('active');
    }
}

// معالجة النموذج
document.getElementById('userForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    username = document.getElementById('username').value;
    platform = document.getElementById('platform').value;
    
    if (username && platform) {
        // عرض البيانات في شاشة النجاح
        document.getElementById('displayUsername').textContent = username;
        document.getElementById('displayPlatform').textContent = platform;
        
        nextStep();
    }
});

// معالجة اختيار العملات
document.addEventListener('DOMContentLoaded', function() {
    const coinOptions = document.querySelectorAll('.coin-option');
    
    coinOptions.forEach(option => {
        option.addEventListener('click', function() {
            // إزالة التحديد من جميع الخيارات
            coinOptions.forEach(opt => opt.classList.remove('selected'));
            
            // إضافة التحديد للخيار المختار
            this.classList.add('selected');
            
            selectedAmount = this.getAttribute('data-amount');
            
            // الانتقال للخطوة التالية بعد ثانية
            setTimeout(() => {
                nextStep();
                startGeneration();
            }, 1000);
        });
    });
});

// بدء عملية التوليد الوهمية
function startGeneration() {
    const steps = document.querySelectorAll('.gen-step');
    const progressRing = document.querySelector('.progress-ring-circle');
    const progressText = document.querySelector('.progress-text');
    
    let progress = 0;
    let stepIndex = 0;
    
    // تحديث شريط التقدم
    const updateProgress = () => {
        progress += Math.random() * 15 + 5;
        if (progress > 100) progress = 100;
        
        const circumference = 2 * Math.PI * 52;
        const strokeDasharray = `${(progress / 100) * circumference} ${circumference}`;
        progressRing.style.strokeDasharray = strokeDasharray;
        progressText.textContent = Math.round(progress) + '%';
        
        if (progress < 100) {
            setTimeout(updateProgress, Math.random() * 500 + 300);
        } else {
            setTimeout(() => {
                nextStep();
                showFinalResults();
            }, 1000);
        }
    };
    
    // تحديث خطوات التوليد
    const updateSteps = () => {
        if (stepIndex < steps.length) {
            // إكمال الخطوة السابقة
            if (stepIndex > 0) {
                steps[stepIndex - 1].classList.remove('active');
                steps[stepIndex - 1].classList.add('completed');
                steps[stepIndex - 1].querySelector('.step-status').textContent = '✓';
            }
            
            // تفعيل الخطوة الحالية
            steps[stepIndex].classList.add('active');
            steps[stepIndex].querySelector('.step-status').textContent = '⏳';
            
            stepIndex++;
            
            if (stepIndex < steps.length) {
                setTimeout(updateSteps, Math.random() * 2000 + 1500);
            } else {
                // إكمال آخر خطوة
                setTimeout(() => {
                    steps[stepIndex - 1].classList.remove('active');
                    steps[stepIndex - 1].classList.add('completed');
                    steps[stepIndex - 1].querySelector('.step-status').textContent = '✓';
                }, 1000);
            }
        }
    };
    
    // بدء العمليات
    updateProgress();
    updateSteps();
}

// عرض النتائج النهائية
function showFinalResults() {
    document.getElementById('finalAmount').textContent = selectedAmount.toLocaleString();
    document.getElementById('finalUsername').textContent = username;
}

// إكمال العملية
function completeProcess() {
    // هنا يمكن إضافة رابط للتحقق البشري أو أي إجراء آخر
    alert('شكراً لك! سيتم توجيهك لإكمال التحقق البشري.');
    
    // يمكن إضافة رابط خارجي هنا
    // window.open('https://example.com/verification', '_blank');
}

// تأثيرات إضافية
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثير الجسيمات للخلفية
    createParticles();
    
    // إضافة أصوات (اختيارية)
    addSoundEffects();
});

// إنشاء جسيمات متحركة
function createParticles() {
    const particlesContainer = document.createElement('div');
    particlesContainer.className = 'particles';
    particlesContainer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: -1;
    `;
    
    document.body.appendChild(particlesContainer);
    
    for (let i = 0; i < 50; i++) {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 50%;
            animation: float ${Math.random() * 3 + 2}s ease-in-out infinite;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation-delay: ${Math.random() * 2}s;
        `;
        
        particlesContainer.appendChild(particle);
    }
    
    // إضافة CSS للأنيميشن
    const style = document.createElement('style');
    style.textContent = `
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 0.5; }
        }
    `;
    document.head.appendChild(style);
}

// إضافة تأثيرات صوتية (اختيارية)
function addSoundEffects() {
    // يمكن إضافة ملفات صوتية هنا
    // const clickSound = new Audio('assets/sounds/click.mp3');
    // const successSound = new Audio('assets/sounds/success.mp3');
    
    // إضافة أصوات للأزرار
    document.querySelectorAll('button, .coin-option').forEach(element => {
        element.addEventListener('click', () => {
            // clickSound.play();
        });
    });
}

// تأثيرات إضافية للتفاعل
document.addEventListener('DOMContentLoaded', function() {
    // تأثير الهز للعناصر المهمة
    const importantElements = document.querySelectorAll('.btn-primary, .coin-option');
    
    importantElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            this.style.animation = 'shake 0.5s ease-in-out';
        });
        
        element.addEventListener('animationend', function() {
            this.style.animation = '';
        });
    });
    
    // إضافة CSS للهز
    const shakeStyle = document.createElement('style');
    shakeStyle.textContent = `
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    `;
    document.head.appendChild(shakeStyle);
});

// تحسين الأداء والتجربة
window.addEventListener('load', function() {
    // إخفاء شاشة التحميل الافتراضية للمتصفح
    document.body.style.visibility = 'visible';
    
    // تحسين الأنيميشن للأجهزة الضعيفة
    if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
        document.body.classList.add('low-performance');
    }
});

// معالجة الأخطاء
window.addEventListener('error', function(e) {
    console.log('حدث خطأ:', e.error);
    // يمكن إضافة معالجة أخطاء مخصصة هنا
});
