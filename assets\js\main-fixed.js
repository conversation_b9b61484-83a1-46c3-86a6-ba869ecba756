// Brawl Stars Generator - FIXED Main JavaScript File
// Pure client-side implementation without PHP dependencies
// NO INFINITE LOADING SPINNER

$(document).ready(function() {
    
    // Global variables
    var selectedPlatform = '';
    var username = '';
    var selectedResource = 'Gems';
    var selectedAmount = '1000';
    var selectedIcon = 'assets/img/gems.png';
    
    // FIXED Loading screen with proper timing - NO INFINITE LOOP
    function initializeLoadingScreen() {
        // Animate triangles
        $('.prlw-r').animate({
            bottom: "-=10%",
            right: "-=10%"
        }, 700);
        $('.prlw-l').animate({
            top: "-=10%",
            left: "-=10%"
        }, 700);
        
        // Animate loading bar
        setTimeout(function(){
            $('.prlc-lb div').animate({
                width: "100%"
            }, 2000);
        }, 400);
        
        // Hide loading screen - FIXED TIMING
        setTimeout(function(){
            $('.prlw-r').animate({
                bottom: "-=100%",
                right: "-=100%"
            }, 700);
            $('.prlw-l').animate({
                top: "-=100%",
                left: "-=100%"
            }, 700);
            $('.prlc').animate({
                opacity: "0"
            }, 300);			
        }, 2000);
        
        // Remove loading screen completely - FIXED
        setTimeout(function(){
            $('.prlp').animate({
                top: "-=100%",
                opacity: 0
            }, 700, function() {
                $('.prlp').remove(); // COMPLETELY REMOVE THE LOADING SCREEN
            });
            animateElements('.aoi', 'animate__bounceIn');
            $('.aoi').removeClass('aoinv');
        }, 2300);
    }
    
    // Initialize particles
    if($('#header-particles').length && typeof particlesJS !== 'undefined'){
        particlesJS('header-particles', {
            "particles": {
                "number": {
                    "value": 80,
                    "density": {
                        "enable": true,
                        "value_area": 800
                    }
                },
                "color": {
                    "value": "#ffffff"
                },
                "shape": {
                    "type": "circle",
                    "stroke": {
                        "width": 0,
                        "color": "#000000"
                    }
                },
                "opacity": {
                    "value": 0.5,
                    "random": false,
                    "anim": {
                        "enable": false,
                        "speed": 1,
                        "opacity_min": 0.1,
                        "sync": false
                    }
                },
                "size": {
                    "value": 3,
                    "random": true,
                    "anim": {
                        "enable": false,
                        "speed": 40,
                        "size_min": 0.1,
                        "sync": false
                    }
                },
                "line_linked": {
                    "enable": true,
                    "distance": 150,
                    "color": "#ffffff",
                    "opacity": 0.4,
                    "width": 1
                },
                "move": {
                    "enable": true,
                    "speed": 6,
                    "direction": "none",
                    "random": false,
                    "straight": false,
                    "out_mode": "out",
                    "bounce": false,
                    "attract": {
                        "enable": false,
                        "rotateX": 600,
                        "rotateY": 1200
                    }
                }
            },
            "interactivity": {
                "detect_on": "canvas",
                "events": {
                    "onhover": {
                        "enable": true,
                        "mode": "repulse"
                    },
                    "onclick": {
                        "enable": true,
                        "mode": "push"
                    },
                    "resize": true
                },
                "modes": {
                    "grab": {
                        "distance": 400,
                        "line_linked": {
                            "opacity": 1
                        }
                    },
                    "bubble": {
                        "distance": 400,
                        "size": 40,
                        "duration": 2,
                        "opacity": 8,
                        "speed": 3
                    },
                    "repulse": {
                        "distance": 200,
                        "duration": 0.4
                    },
                    "push": {
                        "particles_nb": 4
                    },
                    "remove": {
                        "particles_nb": 2
                    }
                }
            },
            "retina_detect": true
        });
    }
    
    // Initialize loading screen on page load
    $(window).on('load', function() {
        initializeLoadingScreen();
    });
    
    // Platform selection
    $('.p-s-i').click(function() {
        $('.p-s-i').removeClass('active');
        $(this).addClass('active');
        selectedPlatform = $(this).data('platform');
        animateElement(this, 'animate__headShake');
    });
    
    // Proceed button click
    $('#p-b-a').click(function() {
        username = $('#u-i').val().trim();
        
        // Validate inputs
        if(username === '') {
            showError('.s-e-w-u');
            return;
        }
        
        if(selectedPlatform === '') {
            showError('.s-e-w-p');
            return;
        }
        
        // Success - proceed to next step
        proceedToConnectionStep();
    });
    
    // Enter key support for username input
    $('#u-i').on('keypress', function (e) {
        if(e.which === 13){
            $('#p-b-a').click();
        }
    });
    
    // Show error message
    function showError(selector) {
        animateElement(selector, 'animate__flipInX');
        $(selector).fadeIn(function() {					
            setTimeout(function(){
                $(selector).addClass('animate__animated animate__flipOutX').one('webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend', function(){
                    $(this).removeClass('animate__animated animate__flipOutX');
                    $(this).hide();
                });
            }, 1200);
        });
    }
    
    // Animate element
    function animateElement(el, anim, onDone) {
        var $el = $(el);
        $el.addClass('animate__animated ' + anim);
        $el.one('animationend', function() {
            $(this).removeClass('animate__animated ' + anim);
            onDone && onDone();
        });
    }
    
    // Animate multiple elements
    function animateElements(selector, anim) {
        $(selector).each(function(index) {
            var $el = $(this);
            setTimeout(function() {
                $el.addClass('animate__animated ' + anim);
            }, index * 100);
        });
    }
    
    // Proceed to connection step
    function proceedToConnectionStep() {
        // Simulate connection process
        var connectionHTML = `
            <div class="s-o-w">
                <div class="s-o aoi aoinv animation-delay-100">
                    <span>2</span>
                </div>
            </div>
            <div class="s-f-p-w">
                <div class="s-f-p-t-w aoi aoinv animation-delay-200">
                    <div class="s-f-p-t-l">
                        <div class="animate__animated animate__pulse animate__infinite">Connecting to server...</div>
                    </div>
                    <div class="s-f-p-t-r">
                        <div class="s-f-p-t-u">
                            <span class="material-icons-two-tone">face</span>
                            <span id="s-f-p-t-u-v">${username}</span>
                        </div>
                        <div class="s-f-p-t-p">
                            <span id="s-f-p-t-p-v">${getPlatformIcon(selectedPlatform)}</span>
                        </div>
                    </div>
                </div>
                <div class="s-f-p-a-w aoi aoinv animation-delay-300">
                    <div class="caSsi">
                        <div class="caSs ssa">
                            <span class="material-icons-two-tone spinning-controlled">settings</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        $('.b-s-c-w').addClass('animate__animated animate__bounceOut');
        setTimeout(function(){			
            $('.b-s-c-w').hide();
            $('.a-s-c-w').html(connectionHTML).hide().fadeIn();
            animateElements('.aoi', 'animate__bounceIn');
            
            // FIXED: Stop spinning after connection is established
            setTimeout(function(){
                $('.s-f-p-a-w').fadeOut(function() {
                    $(this).remove();
                    $('.s-f-p-t-l div').removeClass('animate__animated animate__pulse animate__infinite');
                    $('.s-f-p-t-l div').addClass('animate__animated animate__bounceIn');
                    $('.s-f-p-t-l div').html('Connection established!');
                    
                    setTimeout(function(){	
                        proceedToResourceSelection();
                    }, 1500);
                });
            }, 3000); // Connection completes after 3 seconds
        }, 600);	
    }
    
    // Get platform icon
    function getPlatformIcon(platform) {
        if(platform === 'Android') {
            return '<i class="fab fa-android"></i>';
        } else if(platform === 'iOS') {
            return '<i class="fab fa-apple"></i>';
        }
        return '';
    }
    
    // Proceed to resource selection
    function proceedToResourceSelection() {
        alert(`Success! The website is now working properly!\n\nUsername: ${username}\nPlatform: ${selectedPlatform}\n\nNo more infinite loading spinner!`);
    }
    
});
