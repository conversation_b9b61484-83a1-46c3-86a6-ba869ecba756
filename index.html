<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brawl Stars - Free Gems Generator</title>
    <link rel="icon" href="assets/img/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Background animation -->
    <div class="background-animation"></div>

    <!-- Main container -->
    <div class="container">
        <!-- Step 1: Loading screen -->
        <div class="step active" id="step1">
            <div class="loading-screen">
                <img src="assets/img/game icon.png" alt="Brawl Stars" class="game-logo">
                <h1>Brawl Stars</h1>
                <h2>Free Gems Generator</h2>
                <div class="loading-spinner">
                    <div class="spinner"></div>
                </div>
                <p class="loading-text">Loading...</p>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>
        </div>

        <!-- Step 2: User input -->
        <div class="step" id="step2">
            <div class="form-container">
                <img src="assets/img/game icon.png" alt="Brawl Stars" class="form-logo">
                <h2>Enter Your Details</h2>
                <form id="userForm">
                    <div class="input-group">
                        <label for="username">Username:</label>
                        <input type="text" id="username" name="username" placeholder="Enter your username (min 4 characters)" required minlength="4">
                        <div class="input-error" id="usernameError">Username must be at least 4 characters long</div>
                    </div>
                    <div class="input-group">
                        <label for="platform">Select Platform:</label>
                        <select id="platform" name="platform" required>
                            <option value="">Choose Platform</option>
                            <option value="android">Android</option>
                            <option value="ios">iOS</option>
                            <option value="pc">PC</option>
                        </select>
                    </div>
                    <button type="submit" class="btn-primary">Continue</button>
                </form>
            </div>
        </div>

        <!-- Step 3: Success message -->
        <div class="step" id="step3">
            <div class="success-container">
                <div class="success-icon">✓</div>
                <h2>Verification Successful!</h2>
                <p>Your account has been found successfully</p>
                <div class="user-info">
                    <p><strong>Username:</strong> <span id="displayUsername"></span></p>
                    <p><strong>Platform:</strong> <span id="displayPlatform"></span></p>
                </div>
                <button class="btn-primary" onclick="nextStep()">Continue</button>
            </div>
        </div>

        <!-- Step 4: Select gems amount -->
        <div class="step" id="step4">
            <div class="gems-container">
                <div class="gems-header">
                    <div class="step-number">2</div>
                    <h2>Please select the amount of Gems.</h2>
                </div>

                <div class="selected-amount">
                    <div class="selected-label">SELECTED AMOUNT</div>
                    <div class="gem-display">
                        <div class="gem-icon">💎</div>
                        <div class="gem-count">2500</div>
                        <div class="gem-text">Gems</div>
                    </div>
                </div>

                <div class="gems-options">
                    <div class="gem-option" data-amount="2500">
                        <div class="gem-icon-small">💎</div>
                        <span>2500 Gems</span>
                    </div>
                    <div class="gem-option" data-amount="5000">
                        <div class="gem-icon-small">💎</div>
                        <span>5000 Gems</span>
                    </div>
                    <div class="gem-option" data-amount="7500">
                        <div class="gem-icon-small">💎</div>
                        <span>7500 Gems</span>
                    </div>
                    <div class="gem-option" data-amount="10000">
                        <div class="gem-icon-small">💎</div>
                        <span>10000 Gems</span>
                    </div>
                </div>

                <button class="btn-proceed" onclick="proceedToGeneration()">Proceed</button>
            </div>
        </div>

        <!-- Step 5: Generation process -->
        <div class="step" id="step5">
            <div class="generating-container">
                <h2>Generating Gems...</h2>
                <div class="generation-progress">
                    <div class="progress-circle">
                        <svg class="progress-ring" width="120" height="120">
                            <circle class="progress-ring-circle" stroke="#4CAF50" stroke-width="8" fill="transparent" r="52" cx="60" cy="60"/>
                        </svg>
                        <div class="progress-text">0%</div>
                    </div>
                </div>
                <div class="generation-steps">
                    <div class="gen-step">
                        <span class="step-icon">🔍</span>
                        <span class="step-text">Searching for account...</span>
                        <span class="step-status">✓</span>
                    </div>
                    <div class="gen-step">
                        <span class="step-icon">🔐</span>
                        <span class="step-text">Security verification...</span>
                        <span class="step-status">⏳</span>
                    </div>
                    <div class="gen-step">
                        <span class="step-icon">💎</span>
                        <span class="step-text">Generating gems...</span>
                        <span class="step-status">⏳</span>
                    </div>
                    <div class="gen-step">
                        <span class="step-icon">📤</span>
                        <span class="step-text">Sending gems...</span>
                        <span class="step-status">⏳</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 6: Final step -->
        <div class="step" id="step6">
            <div class="final-container">
                <div class="final-icon">🎉</div>
                <h2>Congratulations!</h2>
                <p>Gems generated successfully</p>
                <div class="final-info">
                    <p><strong>Amount:</strong> <span id="finalAmount"></span> Gems</p>
                    <p><strong>Account:</strong> <span id="finalUsername"></span></p>
                </div>
                <div class="final-note">
                    <p>⚠️ To complete the process, please complete the human verification below</p>
                </div>
                <button class="btn-primary btn-large" onclick="completeProcess()">Complete Verification</button>
            </div>
        </div>
    </div>

    <script src="assets/js/script.js"></script>
</body>
</html>
