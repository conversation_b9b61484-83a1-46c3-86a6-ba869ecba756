<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brawl Stars - مولد العملات المجاني</title>
    <link rel="icon" href="assets/img/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- خلفية متحركة -->
    <div class="background-animation"></div>
    
    <!-- الحاوي الرئيسي -->
    <div class="container">
        <!-- الخطوة 1: شاشة التحميل -->
        <div class="step active" id="step1">
            <div class="loading-screen">
                <img src="assets/img/game icon.png" alt="Brawl Stars" class="game-logo">
                <h1>Brawl Stars</h1>
                <h2>مولد العملات المجاني</h2>
                <div class="loading-spinner">
                    <div class="spinner"></div>
                </div>
                <p class="loading-text">جاري التحميل...</p>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>
        </div>

        <!-- الخطوة 2: إدخال البيانات -->
        <div class="step" id="step2">
            <div class="form-container">
                <img src="assets/img/game icon.png" alt="Brawl Stars" class="form-logo">
                <h2>أدخل بياناتك</h2>
                <form id="userForm">
                    <div class="input-group">
                        <label for="username">اسم المستخدم:</label>
                        <input type="text" id="username" name="username" placeholder="أدخل اسم المستخدم" required>
                    </div>
                    <div class="input-group">
                        <label for="platform">اختر المنصة:</label>
                        <select id="platform" name="platform" required>
                            <option value="">اختر المنصة</option>
                            <option value="android">Android</option>
                            <option value="ios">iOS</option>
                            <option value="pc">PC</option>
                        </select>
                    </div>
                    <button type="submit" class="btn-primary">متابعة</button>
                </form>
            </div>
        </div>

        <!-- الخطوة 3: رسالة النجاح -->
        <div class="step" id="step3">
            <div class="success-container">
                <div class="success-icon">✓</div>
                <h2>تم التحقق بنجاح!</h2>
                <p>تم العثور على حسابك بنجاح</p>
                <div class="user-info">
                    <p><strong>اسم المستخدم:</strong> <span id="displayUsername"></span></p>
                    <p><strong>المنصة:</strong> <span id="displayPlatform"></span></p>
                </div>
                <button class="btn-primary" onclick="nextStep()">متابعة</button>
            </div>
        </div>

        <!-- الخطوة 4: اختيار كمية العملات -->
        <div class="step" id="step4">
            <div class="coins-container">
                <h2>اختر كمية العملات</h2>
                <div class="coins-options">
                    <div class="coin-option" data-amount="1000">
                        <div class="coin-icon">💎</div>
                        <span>1,000 جوهرة</span>
                    </div>
                    <div class="coin-option" data-amount="5000">
                        <div class="coin-icon">💎</div>
                        <span>5,000 جوهرة</span>
                    </div>
                    <div class="coin-option" data-amount="10000">
                        <div class="coin-icon">💎</div>
                        <span>10,000 جوهرة</span>
                    </div>
                    <div class="coin-option" data-amount="25000">
                        <div class="coin-icon">💎</div>
                        <span>25,000 جوهرة</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- الخطوة 5: التحميل الوهمي -->
        <div class="step" id="step5">
            <div class="generating-container">
                <h2>جاري توليد العملات...</h2>
                <div class="generation-progress">
                    <div class="progress-circle">
                        <svg class="progress-ring" width="120" height="120">
                            <circle class="progress-ring-circle" stroke="#4CAF50" stroke-width="8" fill="transparent" r="52" cx="60" cy="60"/>
                        </svg>
                        <div class="progress-text">0%</div>
                    </div>
                </div>
                <div class="generation-steps">
                    <div class="gen-step">
                        <span class="step-icon">🔍</span>
                        <span class="step-text">البحث عن الحساب...</span>
                        <span class="step-status">✓</span>
                    </div>
                    <div class="gen-step">
                        <span class="step-icon">🔐</span>
                        <span class="step-text">التحقق من الأمان...</span>
                        <span class="step-status">⏳</span>
                    </div>
                    <div class="gen-step">
                        <span class="step-icon">💎</span>
                        <span class="step-text">توليد العملات...</span>
                        <span class="step-status">⏳</span>
                    </div>
                    <div class="gen-step">
                        <span class="step-icon">📤</span>
                        <span class="step-text">إرسال العملات...</span>
                        <span class="step-status">⏳</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- الخطوة 6: الخطوة الأخيرة -->
        <div class="step" id="step6">
            <div class="final-container">
                <div class="final-icon">🎉</div>
                <h2>تهانينا!</h2>
                <p>تم توليد العملات بنجاح</p>
                <div class="final-info">
                    <p><strong>الكمية:</strong> <span id="finalAmount"></span> جوهرة</p>
                    <p><strong>الحساب:</strong> <span id="finalUsername"></span></p>
                </div>
                <div class="final-note">
                    <p>⚠️ لإكمال العملية، يرجى إكمال التحقق البشري أدناه</p>
                </div>
                <button class="btn-primary btn-large" onclick="completeProcess()">إكمال التحقق</button>
            </div>
        </div>
    </div>

    <script src="assets/js/script.js"></script>
</body>
</html>
